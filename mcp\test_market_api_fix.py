#!/usr/bin/env python3
"""
验证 market.ts 文件修复是否成功
"""

import re
from pathlib import Path

def check_market_api_exports():
    """检查 market.ts 文件的导出情况"""
    project_root = Path.cwd().parent
    market_file = project_root / "frontend" / "src" / "api" / "market.ts"
    
    if not market_file.exists():
        print("❌ market.ts 文件不存在")
        return False
    
    try:
        content = market_file.read_text(encoding='utf-8')
        
        # 查找所有 marketApi 相关的导出
        marketapi_exports = re.findall(r'export\s+(?:const|default)\s+marketApi', content)
        
        print("🔍 market.ts 导出检查报告")
        print("=" * 40)
        
        print(f"📊 找到 marketApi 导出: {len(marketapi_exports)} 个")
        
        for i, export in enumerate(marketapi_exports, 1):
            print(f"   {i}. {export}")
        
        # 检查是否有重复的 const 导出
        const_exports = re.findall(r'export\s+const\s+marketApi', content)
        
        if len(const_exports) > 1:
            print(f"❌ 发现重复的 const 导出: {len(const_exports)} 个")
            return False
        elif len(const_exports) == 1:
            print("✅ const 导出: 正常 (1个)")
        else:
            print("⚠️ const 导出: 未找到")
        
        # 检查默认导出
        default_exports = re.findall(r'export\s+default\s+marketApi', content)
        
        if len(default_exports) == 1:
            print("✅ default 导出: 正常 (1个)")
        elif len(default_exports) > 1:
            print(f"❌ 发现重复的 default 导出: {len(default_exports)} 个")
            return False
        else:
            print("⚠️ default 导出: 未找到")
        
        # 检查 MarketAPI 类导出
        class_exports = re.findall(r'export\s+class\s+MarketAPI', content)
        
        if len(class_exports) == 1:
            print("✅ MarketAPI 类导出: 正常 (1个)")
        else:
            print(f"⚠️ MarketAPI 类导出: {len(class_exports)} 个")
        
        # 检查实例创建
        instance_creation = re.findall(r'const\s+marketApiInstance\s*=\s*new\s+MarketAPI', content)
        
        if len(instance_creation) == 1:
            print("✅ 实例创建: 正常 (marketApiInstance)")
        else:
            print(f"⚠️ 实例创建: {len(instance_creation)} 个")
        
        # 总体评估
        print("\n🎯 修复结果评估")
        print("-" * 40)
        
        if len(const_exports) <= 1 and len(default_exports) <= 1:
            print("🎉 修复成功！没有重复导出问题")
            print("✅ 编译错误应该已经解决")
            return True
        else:
            print("❌ 仍有重复导出问题，需要进一步修复")
            return False
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_typescript_syntax():
    """检查 TypeScript 语法问题"""
    project_root = Path.cwd().parent
    market_file = project_root / "frontend" / "src" / "api" / "market.ts"
    
    if not market_file.exists():
        return False
    
    try:
        content = market_file.read_text(encoding='utf-8')
        
        print("\n🔧 TypeScript 语法检查")
        print("-" * 40)
        
        # 检查基本语法结构
        has_imports = len(re.findall(r'import.*from', content)) > 0
        has_exports = len(re.findall(r'export', content)) > 0
        has_interfaces = len(re.findall(r'interface\s+\w+', content)) > 0
        has_classes = len(re.findall(r'class\s+\w+', content)) > 0
        has_functions = len(re.findall(r'async\s+\w+\s*\(', content)) > 0
        
        print(f"✅ 导入语句: {'有' if has_imports else '无'}")
        print(f"✅ 导出语句: {'有' if has_exports else '无'}")
        print(f"✅ 接口定义: {'有' if has_interfaces else '无'}")
        print(f"✅ 类定义: {'有' if has_classes else '无'}")
        print(f"✅ 异步函数: {'有' if has_functions else '无'}")
        
        # 检查可能的语法错误
        syntax_issues = []
        
        # 检查未闭合的括号
        open_braces = content.count('{')
        close_braces = content.count('}')
        if open_braces != close_braces:
            syntax_issues.append(f"括号不匹配: {{ {open_braces} 个, }} {close_braces} 个")
        
        # 检查未闭合的圆括号
        open_parens = content.count('(')
        close_parens = content.count(')')
        if open_parens != close_parens:
            syntax_issues.append(f"圆括号不匹配: ( {open_parens} 个, ) {close_parens} 个")
        
        if syntax_issues:
            print("⚠️ 发现潜在语法问题:")
            for issue in syntax_issues:
                print(f"   - {issue}")
        else:
            print("✅ 基本语法检查通过")
        
        return len(syntax_issues) == 0
        
    except Exception as e:
        print(f"❌ 语法检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始验证 market.ts 修复结果...")
    
    # 检查导出问题
    export_ok = check_market_api_exports()
    
    # 检查语法问题
    syntax_ok = check_typescript_syntax()
    
    # 总结
    print(f"\n🏆 最终验证结果")
    print("=" * 40)
    
    if export_ok and syntax_ok:
        print("🎉 修复完全成功！")
        print("✅ 重复导出问题已解决")
        print("✅ TypeScript 语法正常")
        print("🚀 现在可以正常启动前端服务了")
        print("\n💡 建议操作:")
        print("   1. 重新启动前端开发服务器: npm run dev")
        print("   2. 检查是否还有其他编译错误")
        print("   3. 测试交易中心功能")
    elif export_ok:
        print("✅ 导出问题已修复，但可能还有其他语法问题")
        print("🔧 建议检查 TypeScript 语法")
    elif syntax_ok:
        print("✅ 语法正常，但导出问题可能仍存在")
        print("🔧 建议进一步检查导出语句")
    else:
        print("❌ 仍有问题需要修复")
        print("🔧 建议逐步检查和修复问题")
    
    print(f"\n✅ 验证完成！")

if __name__ == "__main__":
    main()
