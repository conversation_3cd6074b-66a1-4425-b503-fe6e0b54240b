<template>
  <div class="miniqmt-trading">
    <!-- 顶部状态栏 -->
    <div class="status-bar">
      <div class="connection-status">
        <el-tag :type="connectionStatus === 'connected' ? 'success' : 'danger'">
          <el-icon><Link /></el-icon>
          {{ connectionStatus === 'connected' ? '交易系统已连接' : '交易系统未连接' }}
        </el-tag>
      </div>

      <div class="account-status">
        <el-tag type="warning">
          <el-icon><User /></el-icon>
          实盘账户: {{ accountInfo.accountId }}
        </el-tag>
        <span class="funds">可用资金: ¥{{ formatMoney(accountInfo.availableFunds) }}</span>
      </div>

      <div class="actions">
        <el-button @click="connectTradingSystem" :loading="connecting" type="primary">
          {{ connectionStatus === 'connected' ? '重新连接' : '连接交易系统' }}
        </el-button>
        <el-button @click="showSettings = true" type="info" plain>
          <el-icon><Setting /></el-icon>
          设置
        </el-button>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <div v-if="connectionStatus !== 'connected'" class="connection-guide">
        <div class="guide-content">
          <el-icon class="guide-icon"><Warning /></el-icon>
          <h2>实盘交易系统连接指南</h2>
          <div class="guide-steps">
            <div class="step">
              <div class="step-number">1</div>
              <div class="step-content">
                <h4>启动交易客户端</h4>
                <p>请确保交易客户端已经启动并登录</p>
              </div>
            </div>
            <div class="step">
              <div class="step-number">2</div>
              <div class="step-content">
                <h4>检查API设置</h4>
                <p>确认交易系统的API接口已开启，端口设置正确</p>
              </div>
            </div>
            <div class="step">
              <div class="step-number">3</div>
              <div class="step-content">
                <h4>点击连接</h4>
                <p>点击上方"连接交易系统"按钮建立连接</p>
              </div>
            </div>
          </div>

          <div class="guide-actions">
            <el-button @click="connectTradingSystem" :loading="connecting" type="primary" size="large">
              <el-icon><Link /></el-icon>
              连接交易系统
            </el-button>
            <el-button @click="showSettings = true" type="info" size="large">
              <el-icon><Setting /></el-icon>
              连接设置
            </el-button>
          </div>
        </div>
      </div>

      <!-- 已连接状态的交易界面 -->
      <div v-else class="trading-interface">
        <!-- 交易模式切换 -->
        <div class="mode-switcher">
          <div class="mode-tabs">
            <el-radio-group v-model="tradingMode" size="large">
              <el-radio-button label="normal">
                <el-icon><TrendCharts /></el-icon>
                普通交易
              </el-radio-button>
              <el-radio-button label="hft">
                <el-icon><Lightning /></el-icon>
                高频交易
              </el-radio-button>
            </el-radio-group>
          </div>
          <div class="mode-status">
            <el-tag :type="tradingMode === 'hft' ? 'danger' : 'success'" size="large">
              {{ tradingMode === 'hft' ? '高频模式' : '普通模式' }}
            </el-tag>
            <span class="latency">延迟: {{ latency }}ms</span>
          </div>
        </div>

        <!-- 双引擎交易终端 -->
        <div class="dual-trading-terminals">
          <!-- 高频交易终端 -->
          <div class="hft-terminal" :class="{ active: tradingMode === 'hft' }">
            <div class="terminal-header">
              <h3>
                <el-icon><Lightning /></el-icon>
                高频交易终端
              </h3>
              <div class="hft-controls">
                <el-button size="small" type="danger" @click="emergencyStop">
                  <el-icon><CircleClose /></el-icon>
                  紧急停止
                </el-button>
              </div>
            </div>

            <div class="hft-content">
              <!-- Level-3盘口 -->
              <div class="level3-orderbook">
                <div class="orderbook-header">
                  <span>{{ hftStock.code }} {{ hftStock.name }}</span>
                  <span class="price" :class="getPriceClass(hftStock.changePercent)">
                    {{ formatPrice(hftStock.currentPrice) }}
                    <small>{{ hftStock.changePercent >= 0 ? '+' : '' }}{{ hftStock.changePercent.toFixed(2) }}%</small>
                  </span>
                </div>

                <div class="orderbook-data">
                  <div class="asks">
                    <div v-for="(ask, index) in level3Data.asks" :key="'ask-' + index"
                         class="order-level ask" :class="{ 'large-order': ask.volume > 50 }">
                      <span class="price">{{ formatPrice(ask.price) }}</span>
                      <span class="volume">{{ ask.volume }}手</span>
                      <div class="volume-bar" :style="{ width: (ask.volume / maxVolume * 100) + '%' }"></div>
                    </div>
                  </div>

                  <div class="spread">
                    <span>价差: {{ formatPrice(spread) }}</span>
                  </div>

                  <div class="bids">
                    <div v-for="(bid, index) in level3Data.bids" :key="'bid-' + index"
                         class="order-level bid" :class="{ 'large-order': bid.volume > 50 }">
                      <span class="price">{{ formatPrice(bid.price) }}</span>
                      <span class="volume">{{ bid.volume }}手</span>
                      <div class="volume-bar" :style="{ width: (bid.volume / maxVolume * 100) + '%' }"></div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 策略控制台 -->
              <div class="strategy-console">
                <h4>策略控制台</h4>
                <div class="strategy-list">
                  <div v-for="strategy in hftStrategies" :key="strategy.id" class="strategy-item">
                    <div class="strategy-info">
                      <span class="name">{{ strategy.name }}</span>
                      <el-tag :type="strategy.status === 'running' ? 'success' : 'info'" size="small">
                        {{ strategy.status === 'running' ? '运行中' : '已停止' }}
                      </el-tag>
                    </div>
                    <div class="strategy-controls">
                      <el-button size="small"
                                 :type="strategy.status === 'running' ? 'danger' : 'primary'"
                                 @click="toggleStrategy(strategy)">
                        {{ strategy.status === 'running' ? '停止' : '启动' }}
                      </el-button>
                    </div>
                  </div>
                </div>

                <div class="quick-actions">
                  <el-button type="primary" size="small" @click="flashBuy">
                    闪电买入 (Ctrl+B)
                  </el-button>
                  <el-button type="danger" size="small" @click="flashSell">
                    闪电卖出 (Ctrl+S)
                  </el-button>
                  <el-button type="warning" size="small" @click="cancelAllOrders">
                    撤销全部 (~)
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 普通交易终端 -->
          <div class="normal-terminal" :class="{ active: tradingMode === 'normal' }">
            <div class="terminal-header">
              <h3>
                <el-icon><TrendCharts /></el-icon>
                普通交易终端
              </h3>
            </div>

            <div class="normal-content">
              <!-- 股票搜索 -->
              <div class="stock-search">
                <el-input
                  v-model="stockCode"
                  placeholder="输入股票代码或拼音首字母"
                  @keyup.enter="loadStockInfo"
                  size="large"
                >
                  <template #prepend>股票</template>
                  <template #append>
                    <el-button @click="loadStockInfo" :loading="loadingStock">
                      <el-icon><Search /></el-icon>
                    </el-button>
                  </template>
                </el-input>
              </div>

              <!-- 股票信息和十档盘口 -->
              <div v-if="currentStock" class="stock-trading-panel">
                <div class="stock-chart-area">
                  <div class="stock-header">
                    <h3>{{ currentStock.code }} {{ currentStock.name }}</h3>
                    <el-tag :type="currentStock.changePercent >= 0 ? 'danger' : 'success'">
                      {{ currentStock.changePercent >= 0 ? '涨' : '跌' }}
                    </el-tag>
                  </div>

                  <div class="price-display">
                    <div class="current-price" :class="getPriceClass(currentStock.changePercent)">
                      ¥{{ formatPrice(currentStock.currentPrice) }}
                    </div>
                    <div class="price-change">
                      <span :class="getPriceClass(currentStock.changePercent)">
                        {{ currentStock.changePercent >= 0 ? '+' : '' }}{{ currentStock.changePercent.toFixed(2) }}%
                      </span>
                    </div>
                    <div class="price-limits">
                      <span class="limit-up">涨停: ¥{{ formatPrice(currentStock.limitUp) }}</span>
                      <span class="limit-down">跌停: ¥{{ formatPrice(currentStock.limitDown) }}</span>
                    </div>
                  </div>
                </div>

                <div class="level1-orderbook">
                  <h4>十档盘口</h4>
                  <div class="orderbook-simple">
                    <div class="asks-simple">
                      <div v-for="i in 5" :key="'ask-' + i" class="order-level-simple ask">
                        <span class="label">卖{{ 6-i }}</span>
                        <span class="price">{{ formatPrice(12.38 + (6-i) * 0.01) }}</span>
                        <span class="volume">{{ Math.floor(Math.random() * 500) + 100 }}手</span>
                      </div>
                    </div>
                    <div class="bids-simple">
                      <div v-for="i in 5" :key="'bid-' + i" class="order-level-simple bid">
                        <span class="label">买{{ i }}</span>
                        <span class="price">{{ formatPrice(12.37 - (i-1) * 0.01) }}</span>
                        <span class="volume">{{ Math.floor(Math.random() * 500) + 100 }}手</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 交易表单 -->
              <div class="normal-trading-form">
                <el-form :model="orderForm" label-width="80px">
                  <el-form-item label="交易方向">
                    <el-radio-group v-model="orderForm.side" size="large">
                      <el-radio-button label="buy" class="buy-button">买入</el-radio-button>
                      <el-radio-button label="sell" class="sell-button">卖出</el-radio-button>
                    </el-radio-group>
                  </el-form-item>

                  <el-form-item label="委托价格">
                    <el-input-number
                      v-model="orderForm.price"
                      :precision="2"
                      :step="0.01"
                      style="width: 100%"
                      size="large"
                    />
                    <div class="price-helpers">
                      <el-button size="small" @click="setPrice('buy1')">买一价</el-button>
                      <el-button size="small" @click="setPrice('sell1')">卖一价</el-button>
                      <el-button size="small" @click="setPrice('market')">市价</el-button>
                    </div>
                  </el-form-item>

                  <el-form-item label="委托数量">
                    <el-input-number
                      v-model="orderForm.quantity"
                      :min="100"
                      :step="100"
                      style="width: 100%"
                      size="large"
                    />
                    <div class="quantity-helpers">
                      <el-button size="small" @click="setQuantity('quarter')">1/4仓</el-button>
                      <el-button size="small" @click="setQuantity('half')">半仓</el-button>
                      <el-button size="small" @click="setQuantity('full')">全仓</el-button>
                    </div>
                  </el-form-item>

                  <el-form-item>
                    <div class="order-info">
                      <span>可买: {{ availableShares }}股</span>
                      <span>手续费: ¥{{ calculateFee() }}</span>
                    </div>
                  </el-form-item>

                  <el-form-item>
                    <el-button
                      :type="orderForm.side === 'buy' ? 'danger' : 'success'"
                      @click="submitOrder"
                      :loading="submittingOrder"
                      style="width: 100%"
                      size="large"
                    >
                      {{ orderForm.side === 'buy' ? '买入' : '卖出' }}
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </div>
        </div>

        <!-- 实时监控与风控面板 -->
        <div class="risk-control-panel">
          <div class="panel-header">
            <h3>
              <el-icon><Monitor /></el-icon>
              实时监控与风控
            </h3>
            <div class="system-status">
              <el-tag type="success">● 系统正常</el-tag>
              <span class="latency">延迟: {{ latency }}ms</span>
              <el-tag type="warning">合规: 已就绪</el-tag>
            </div>
          </div>

          <div class="monitoring-content">
            <!-- 高频策略监控 -->
            <div class="hft-monitoring">
              <h4>高频策略监控</h4>
              <div class="strategy-status-list">
                <div v-for="strategy in hftStrategies" :key="strategy.id" class="strategy-status">
                  <span class="strategy-name">[{{ strategy.name }}]</span>
                  <el-tag :type="strategy.status === 'running' ? 'success' : 'danger'" size="small">
                    {{ strategy.status === 'running' ? '运行中 √' : '已停止 ×' }}
                  </el-tag>
                  <span class="strategy-pnl" :class="strategy.pnl >= 0 ? 'profit' : 'loss'">
                    {{ strategy.pnl >= 0 ? '+' : '' }}¥{{ formatMoney(Math.abs(strategy.pnl)) }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 普通交易监控 -->
            <div class="normal-monitoring">
              <h4>普通交易监控</h4>
              <div class="trading-stats">
                <div class="stat-item">
                  <span class="label">当日盈亏:</span>
                  <span class="value profit">+¥2,450</span>
                </div>
                <div class="stat-item">
                  <span class="label">持仓风险度:</span>
                  <span class="value">63%</span>
                </div>
                <div class="stat-item">
                  <span class="label">最大回撤:</span>
                  <span class="value loss">-1.2%</span>
                </div>
              </div>
            </div>

            <!-- 实时预警 -->
            <div class="alert-panel">
              <h4>实时预警</h4>
              <div class="alert-list">
                <div v-for="alert in realtimeAlerts" :key="alert.id" class="alert-item" :class="alert.type">
                  <span class="time">{{ alert.time }}</span>
                  <span class="message">{{ alert.message }}</span>
                  <el-button size="small" type="text" @click="dismissAlert(alert.id)">×</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 委托和持仓查询标签页 -->
        <el-tabs v-model="activeTab" type="card" class="data-tabs">
          <el-tab-pane label="委托查询" name="orders">
            <div class="orders-content">
              <div class="orders-header">
                <h3>当日委托</h3>
                <el-button @click="refreshOrders" :loading="loadingOrders">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>

              <el-table :data="orders" stripe>
                <el-table-column prop="orderTime" label="委托时间" width="120" />
                <el-table-column prop="stockCode" label="证券代码" width="100" />
                <el-table-column prop="stockName" label="证券名称" width="120" />
                <el-table-column prop="side" label="买卖方向" width="80">
                  <template #default="{ row }">
                    <el-tag :type="row.side === 'buy' ? 'danger' : 'success'">
                      {{ row.side === 'buy' ? '买入' : '卖出' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="quantity" label="委托数量" width="100" />
                <el-table-column prop="price" label="委托价格" width="100" />
                <el-table-column prop="status" label="委托状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getOrderStatusType(row.status)">
                      {{ getOrderStatusText(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                  <template #default="{ row }">
                    <el-button
                      v-if="row.status === 'pending'"
                      type="text"
                      size="small"
                      @click="cancelOrder(row)"
                    >
                      撤单
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="持仓查询" name="positions">
            <div class="positions-content">
              <div class="positions-header">
                <h3>当前持仓</h3>
                <el-button @click="refreshPositions" :loading="loadingPositions">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>

              <el-table :data="positions" stripe>
                <el-table-column prop="stockCode" label="证券代码" width="100" />
                <el-table-column prop="stockName" label="证券名称" width="120" />
                <el-table-column prop="totalQuantity" label="总持仓" width="100" />
                <el-table-column prop="availableQuantity" label="可用数量" width="100" />
                <el-table-column prop="avgCost" label="成本价" width="100">
                  <template #default="{ row }">
                    ¥{{ formatPrice(row.avgCost) }}
                  </template>
                </el-table-column>
                <el-table-column prop="currentPrice" label="现价" width="100">
                  <template #default="{ row }">
                    ¥{{ formatPrice(row.currentPrice) }}
                  </template>
                </el-table-column>
                <el-table-column prop="marketValue" label="市值" width="120">
                  <template #default="{ row }">
                    ¥{{ formatMoney(row.marketValue) }}
                  </template>
                </el-table-column>
                <el-table-column prop="pnl" label="盈亏" width="120">
                  <template #default="{ row }">
                    <span :class="row.pnl >= 0 ? 'profit' : 'loss'">
                      {{ row.pnl >= 0 ? '+' : '' }}¥{{ formatMoney(Math.abs(row.pnl)) }}
                    </span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 连接设置对话框 -->
    <el-dialog v-model="showSettings" title="MiniQMT连接设置" width="500px">
      <el-form :model="settings" label-width="100px">
        <el-form-item label="服务器地址">
          <el-input v-model="settings.host" placeholder="localhost" />
        </el-form-item>
        <el-form-item label="端口">
          <el-input-number v-model="settings.port" :min="1" :max="65535" style="width: 100%" />
        </el-form-item>
        <el-form-item label="账户ID">
          <el-input v-model="settings.accountId" placeholder="请输入账户ID" />
        </el-form-item>
        <el-form-item label="超时时间">
          <el-input-number v-model="settings.timeout" :min="1000" :max="30000" style="width: 100%" />
          <span style="margin-left: 8px; color: #909399;">毫秒</span>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showSettings = false">取消</el-button>
        <el-button type="primary" @click="saveSettings">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Link, User, Setting, Warning, Search, Refresh,
  TrendCharts, Lightning, CircleClose, Monitor
} from '@element-plus/icons-vue'

// 响应式数据
const connectionStatus = ref<'connected' | 'disconnected' | 'connecting'>('disconnected')
const connecting = ref(false)
const showSettings = ref(false)
const activeTab = ref('orders')
const tradingMode = ref<'normal' | 'hft'>('normal')
const latency = ref(17)

const accountInfo = reactive({
  accountId: 'A123456789',
  availableFunds: 100000.00
})

const settings = reactive({
  host: 'localhost',
  port: 58610,
  accountId: 'A123456789',
  timeout: 5000
})

const stockCode = ref('')
const loadingStock = ref(false)
const currentStock = ref(null)
const availableShares = ref(3800)

// 高频交易相关数据
const hftStock = reactive({
  code: 'IC2406',
  name: '中证500期货',
  currentPrice: 5386.4,
  changePercent: 1.2
})

const level3Data = reactive({
  asks: [
    { price: 5386.8, volume: 18 },
    { price: 5386.6, volume: 22 },
    { price: 5386.4, volume: 41 },
  ],
  bids: [
    { price: 5386.2, volume: 32 },
    { price: 5386.0, volume: 28 },
    { price: 5385.8, volume: 15 },
  ]
})

const hftStrategies = ref([
  { id: 1, name: '套利引擎', status: 'running', pnl: 1250.50 },
  { id: 2, name: '做市策略', status: 'stopped', pnl: -320.80 },
  { id: 3, name: '狙击手', status: 'running', pnl: 890.30 }
])

const realtimeAlerts = ref([
  {
    id: 1,
    time: '14:25:03',
    message: 'IC2406价差突破阈值 → 自动暂停套利策略',
    type: 'warning'
  },
  {
    id: 2,
    time: '14:26:17',
    message: '平安银行大单连续买入 → 弹窗提示',
    type: 'info'
  }
])

const orderForm = reactive({
  side: 'buy',
  orderType: 'limit',
  quantity: 100,
  price: 0
})

const submittingOrder = ref(false)
const loadingOrders = ref(false)
const loadingPositions = ref(false)

const orders = ref([])
const positions = ref([])

// 计算属性
const formatPrice = (price: number) => price.toFixed(2)
const formatMoney = (amount: number) => amount.toFixed(2)

const getPriceClass = (changePercent: number) => {
  if (changePercent > 0) return 'price-up'
  if (changePercent < 0) return 'price-down'
  return 'price-neutral'
}

const getOrderStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    filled: 'success',
    cancelled: 'info',
    rejected: 'danger'
  }
  return statusMap[status] || 'default'
}

const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待成交',
    filled: '已成交',
    cancelled: '已撤销',
    rejected: '已拒绝'
  }
  return statusMap[status] || status
}

// 新增计算属性
const maxVolume = computed(() => {
  const allVolumes = [...level3Data.asks, ...level3Data.bids].map(item => item.volume)
  return Math.max(...allVolumes)
})

const spread = computed(() => {
  if (level3Data.asks.length > 0 && level3Data.bids.length > 0) {
    return level3Data.asks[level3Data.asks.length - 1].price - level3Data.bids[0].price
  }
  return 0
})

const calculateFee = () => {
  const amount = orderForm.quantity * orderForm.price
  return Math.max(amount * 0.0003, 5).toFixed(2) // 最低5元手续费
}

// 方法
const connectTradingSystem = async () => {
  connecting.value = true
  connectionStatus.value = 'connecting'

  try {
    // 模拟连接延迟
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 这里应该调用实际的交易系统API连接
    connectionStatus.value = 'connected'
    ElMessage.success('交易系统连接成功')

    // 连接成功后刷新数据
    await Promise.all([
      refreshOrders(),
      refreshPositions(),
      startRealtimeData()
    ])

  } catch (error) {
    connectionStatus.value = 'disconnected'
    ElMessage.error('交易系统连接失败')
  } finally {
    connecting.value = false
  }
}

// 高频交易相关方法
const emergencyStop = () => {
  ElMessageBox.confirm('确定要紧急停止所有高频策略吗？', '紧急停止', {
    type: 'warning',
    confirmButtonText: '立即停止',
    cancelButtonText: '取消'
  }).then(() => {
    hftStrategies.value.forEach(strategy => {
      strategy.status = 'stopped'
    })
    ElMessage.success('所有高频策略已紧急停止')
  })
}

const toggleStrategy = (strategy: any) => {
  if (strategy.status === 'running') {
    strategy.status = 'stopped'
    ElMessage.success(`策略 ${strategy.name} 已停止`)
  } else {
    strategy.status = 'running'
    ElMessage.success(`策略 ${strategy.name} 已启动`)
  }
}

const flashBuy = () => {
  if (tradingMode.value === 'hft') {
    ElMessage.success('闪电买入执行成功')
  }
}

const flashSell = () => {
  if (tradingMode.value === 'hft') {
    ElMessage.success('闪电卖出执行成功')
  }
}

const cancelAllOrders = () => {
  ElMessageBox.confirm('确定要撤销所有未成交委托吗？', '撤销确认').then(() => {
    ElMessage.success('所有委托已撤销')
  })
}

// 普通交易相关方法
const setPrice = (type: string) => {
  switch (type) {
    case 'buy1':
      orderForm.price = 12.37
      break
    case 'sell1':
      orderForm.price = 12.38
      break
    case 'market':
      orderForm.price = 0
      break
  }
}

const setQuantity = (type: string) => {
  const maxShares = Math.floor(accountInfo.availableFunds / orderForm.price / 100) * 100
  switch (type) {
    case 'quarter':
      orderForm.quantity = Math.floor(maxShares / 4 / 100) * 100
      break
    case 'half':
      orderForm.quantity = Math.floor(maxShares / 2 / 100) * 100
      break
    case 'full':
      orderForm.quantity = maxShares
      break
  }
}

const dismissAlert = (alertId: number) => {
  const index = realtimeAlerts.value.findIndex(alert => alert.id === alertId)
  if (index > -1) {
    realtimeAlerts.value.splice(index, 1)
  }
}

const startRealtimeData = () => {
  // 模拟实时数据更新
  setInterval(() => {
    // 更新延迟
    latency.value = Math.floor(Math.random() * 50) + 10

    // 更新Level-3数据
    level3Data.asks.forEach(ask => {
      ask.volume = Math.floor(Math.random() * 100) + 10
    })
    level3Data.bids.forEach(bid => {
      bid.volume = Math.floor(Math.random() * 100) + 10
    })

    // 更新策略盈亏
    hftStrategies.value.forEach(strategy => {
      if (strategy.status === 'running') {
        strategy.pnl += (Math.random() - 0.5) * 10
      }
    })
  }, 1000)
}

const loadStockInfo = async () => {
  if (!stockCode.value.trim()) {
    ElMessage.warning('请输入股票代码')
    return
  }

  loadingStock.value = true

  try {
    // 模拟股票信息加载
    await new Promise(resolve => setTimeout(resolve, 1000))

    const basePrice = 12.45
    currentStock.value = {
      code: stockCode.value,
      name: '测试股票',
      currentPrice: basePrice,
      changePercent: 1.88,
      limitUp: basePrice * 1.1,  // 涨停价
      limitDown: basePrice * 0.9  // 跌停价
    }

    orderForm.price = currentStock.value.currentPrice

    // 计算可买股数
    availableShares.value = Math.floor(accountInfo.availableFunds / currentStock.value.currentPrice / 100) * 100

  } catch (error) {
    ElMessage.error('股票信息加载失败')
  } finally {
    loadingStock.value = false
  }
}

const submitOrder = async () => {
  if (!currentStock.value) {
    ElMessage.warning('请先选择股票')
    return
  }

  submittingOrder.value = true

  try {
    // 模拟订单提交
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('订单提交成功')

    // 刷新委托查询
    await refreshOrders()

  } catch (error) {
    ElMessage.error('订单提交失败')
  } finally {
    submittingOrder.value = false
  }
}

const refreshOrders = async () => {
  loadingOrders.value = true

  try {
    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 500))

    orders.value = [
      {
        orderTime: '09:30:15',
        stockCode: '000001',
        stockName: '平安银行',
        side: 'buy',
        quantity: 100,
        price: 12.45,
        status: 'pending'
      }
    ]

  } catch (error) {
    ElMessage.error('委托查询失败')
  } finally {
    loadingOrders.value = false
  }
}

const refreshPositions = async () => {
  loadingPositions.value = true

  try {
    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 500))

    positions.value = [
      {
        stockCode: '000001',
        stockName: '平安银行',
        totalQuantity: 1000,
        availableQuantity: 1000,
        avgCost: 12.30,
        currentPrice: 12.45,
        marketValue: 12450,
        pnl: 150
      }
    ]

  } catch (error) {
    ElMessage.error('持仓查询失败')
  } finally {
    loadingPositions.value = false
  }
}

const cancelOrder = async (order: any) => {
  try {
    await ElMessageBox.confirm('确定要撤销这个委托吗？', '撤单确认')

    // 模拟撤单
    await new Promise(resolve => setTimeout(resolve, 500))

    ElMessage.success('撤单成功')
    await refreshOrders()

  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('撤单失败')
    }
  }
}

const saveSettings = () => {
  ElMessage.success('设置已保存')
  showSettings.value = false
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (tradingMode.value === 'hft') {
    if (event.ctrlKey && event.key === 'b') {
      event.preventDefault()
      flashBuy()
    } else if (event.ctrlKey && event.key === 's') {
      event.preventDefault()
      flashSell()
    } else if (event.key === '`') {
      event.preventDefault()
      cancelAllOrders()
    }
  }
}

onMounted(() => {
  console.log('实盘交易中心初始化完成')

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)

  // 模拟初始数据
  if (connectionStatus.value === 'connected') {
    startRealtimeData()
  }
})

onUnmounted(() => {
  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped lang="scss">
.miniqmt-trading {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.status-bar {
  background: white;
  padding: 12px 24px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .connection-status {
    .el-tag {
      .el-icon {
        margin-right: 6px;
      }
    }
  }

  .account-status {
    display: flex;
    align-items: center;
    gap: 16px;

    .funds {
      color: #67c23a;
      font-weight: 600;
    }
  }

  .actions {
    display: flex;
    gap: 12px;
  }
}

// 模式切换器
.mode-switcher {
  background: white;
  padding: 16px 24px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .mode-tabs {
    .el-radio-button {
      .el-icon {
        margin-right: 8px;
      }
    }
  }

  .mode-status {
    display: flex;
    align-items: center;
    gap: 16px;

    .latency {
      color: #909399;
      font-size: 14px;
    }
  }
}

// 双引擎交易终端
.dual-trading-terminals {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  padding: 16px;
  flex: 1;
  overflow: hidden;

  .hft-terminal,
  .normal-terminal {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;

    &:not(.active) {
      opacity: 0.6;
      transform: scale(0.98);
    }

    .terminal-header {
      padding: 16px 20px;
      border-bottom: 1px solid #e4e7ed;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 8px 8px 0 0;

      h3 {
        margin: 0;
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }

  .hft-terminal .terminal-header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  }
}

// 高频交易终端内容
.hft-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 16px;
  padding: 16px;
  flex: 1;
  overflow: hidden;

  .level3-orderbook {
    .orderbook-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e4e7ed;

      .price {
        font-size: 18px;
        font-weight: 600;

        small {
          font-size: 12px;
          margin-left: 8px;
        }
      }
    }

    .orderbook-data {
      .asks, .bids {
        margin: 8px 0;
      }

      .order-level {
        display: grid;
        grid-template-columns: 80px 80px 1fr;
        align-items: center;
        padding: 4px 8px;
        margin: 2px 0;
        border-radius: 4px;
        position: relative;

        &.large-order {
          border: 2px solid #ffd700;
          background: rgba(255, 215, 0, 0.1);
        }

        &.ask {
          background: rgba(245, 108, 108, 0.1);

          .volume-bar {
            background: #f56c6c;
            height: 100%;
            position: absolute;
            right: 0;
            top: 0;
            opacity: 0.3;
            border-radius: 0 4px 4px 0;
          }
        }

        &.bid {
          background: rgba(103, 194, 58, 0.1);

          .volume-bar {
            background: #67c23a;
            height: 100%;
            position: absolute;
            right: 0;
            top: 0;
            opacity: 0.3;
            border-radius: 0 4px 4px 0;
          }
        }

        .price {
          font-weight: 600;
        }

        .volume {
          color: #606266;
          font-size: 12px;
        }
      }

      .spread {
        text-align: center;
        padding: 8px;
        background: #f8f9fa;
        border-radius: 4px;
        margin: 8px 0;
        font-weight: 600;
        color: #409eff;
      }
    }
  }

  .strategy-console {
    h4 {
      margin: 0 0 16px 0;
      color: #303133;
    }

    .strategy-list {
      margin-bottom: 20px;

      .strategy-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 8px;

        .strategy-info {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .name {
            font-weight: 600;
            color: #303133;
          }
        }
      }
    }

    .quick-actions {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .el-button {
        width: 100%;
      }
    }
  }
}

.main-content {
  flex: 1;
  overflow: auto;
}

.connection-guide {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;

  .guide-content {
    background: white;
    border-radius: 12px;
    padding: 40px;
    text-align: center;
    max-width: 600px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

    .guide-icon {
      font-size: 64px;
      color: #f56c6c;
      margin-bottom: 24px;
    }

    h2 {
      margin: 0 0 32px 0;
      color: #303133;
    }

    .guide-steps {
      display: flex;
      flex-direction: column;
      gap: 24px;
      margin-bottom: 32px;

      .step {
        display: flex;
        align-items: flex-start;
        gap: 16px;
        text-align: left;

        .step-number {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background: #409eff;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          flex-shrink: 0;
        }

        .step-content {
          h4 {
            margin: 0 0 8px 0;
            color: #303133;
          }

          p {
            margin: 0;
            color: #606266;
            line-height: 1.6;
          }
        }
      }
    }

    .guide-actions {
      display: flex;
      gap: 16px;
      justify-content: center;
    }
  }
}

.trading-interface {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 100%;

  .trading-content {
    padding: 20px;

    .trading-panels {
      display: grid;
      grid-template-columns: 1fr 400px;
      gap: 24px;
      height: 500px;

      .left-panel {
        .stock-selector {
          margin-bottom: 20px;
        }

        .stock-info {
          background: #f8f9fa;
          border-radius: 8px;
          padding: 20px;

          .stock-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;

            h3 {
              margin: 0;
              color: #303133;
            }
          }

          .price-display {
            .current-price {
              font-size: 32px;
              font-weight: 700;
              margin-bottom: 8px;

              &.price-up { color: #f56c6c; }
              &.price-down { color: #67c23a; }
              &.price-neutral { color: #909399; }
            }

            .price-change {
              font-size: 16px;
              font-weight: 600;

              span {
                &.price-up { color: #f56c6c; }
                &.price-down { color: #67c23a; }
                &.price-neutral { color: #909399; }
              }
            }
          }
        }
      }

      .right-panel {
        .trading-form {
          background: #f8f9fa;
          border-radius: 8px;
          padding: 20px;
        }
      }
    }
  }

  .orders-content,
  .positions-content {
    padding: 20px;

    .orders-header,
    .positions-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      h3 {
        margin: 0;
        color: #303133;
      }
    }

    .profit {
      color: #f56c6c;
    }

    .loss {
      color: #67c23a;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .trading-panels {
    grid-template-columns: 1fr !important;
    grid-template-rows: auto 1fr;
    height: auto !important;
  }
}

@media (max-width: 768px) {
  .status-bar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;

    .account-status {
      justify-content: center;
    }

    .actions {
      justify-content: center;
    }
  }

  .main-content {
    padding: 16px;
  }
}
</style>
