#!/usr/bin/env python3
"""
前端空白页面诊断脚本
分析可能导致页面空白的原因
"""

import os
import re
from pathlib import Path
from typing import Dict, List, Any

def check_frontend_files():
    """检查前端关键文件是否存在"""
    project_root = Path.cwd().parent
    frontend_path = project_root / "frontend"
    
    critical_files = {
        "package.json": frontend_path / "package.json",
        "vite.config.ts": frontend_path / "vite.config.ts", 
        "index.html": frontend_path / "index.html",
        "main.ts": frontend_path / "src" / "main.ts",
        "App.vue": frontend_path / "src" / "App.vue",
        "router/index.ts": frontend_path / "src" / "router" / "index.ts",
        "config/index.ts": frontend_path / "src" / "config" / "index.ts"
    }
    
    print("🔍 前端关键文件检查")
    print("=" * 50)
    
    missing_files = []
    for name, path in critical_files.items():
        if path.exists():
            print(f"✅ {name}: 存在")
        else:
            print(f"❌ {name}: 缺失")
            missing_files.append(name)
    
    return len(missing_files) == 0

def check_dependencies():
    """检查依赖是否正确安装"""
    project_root = Path.cwd().parent
    frontend_path = project_root / "frontend"
    
    print(f"\n📦 依赖检查")
    print("-" * 30)
    
    # 检查 node_modules
    node_modules = frontend_path / "node_modules"
    if node_modules.exists():
        print("✅ node_modules: 存在")
        
        # 检查关键依赖
        key_deps = ["vue", "vue-router", "element-plus", "vite"]
        for dep in key_deps:
            dep_path = node_modules / dep
            if dep_path.exists():
                print(f"✅ {dep}: 已安装")
            else:
                print(f"❌ {dep}: 未安装")
    else:
        print("❌ node_modules: 不存在")
        return False
    
    # 检查 package.json
    package_json = frontend_path / "package.json"
    if package_json.exists():
        try:
            import json
            with open(package_json, 'r', encoding='utf-8') as f:
                pkg = json.load(f)
            
            print(f"📋 项目名称: {pkg.get('name', 'Unknown')}")
            print(f"📋 项目版本: {pkg.get('version', 'Unknown')}")
            
            deps = pkg.get('dependencies', {})
            dev_deps = pkg.get('devDependencies', {})
            
            print(f"📋 生产依赖: {len(deps)} 个")
            print(f"📋 开发依赖: {len(dev_deps)} 个")
            
        except Exception as e:
            print(f"❌ package.json 解析失败: {e}")
            return False
    
    return True

def check_config_issues():
    """检查配置文件问题"""
    project_root = Path.cwd().parent
    frontend_path = project_root / "frontend"
    
    print(f"\n⚙️ 配置文件检查")
    print("-" * 30)
    
    # 检查 vite.config.ts
    vite_config = frontend_path / "vite.config.ts"
    if vite_config.exists():
        try:
            content = vite_config.read_text(encoding='utf-8')
            
            # 检查基本配置
            has_vue_plugin = '@vitejs/plugin-vue' in content
            has_port_config = 'port:' in content or 'port =' in content
            has_proxy_config = 'proxy:' in content or 'proxy =' in content
            
            print(f"✅ vite.config.ts: 存在")
            print(f"{'✅' if has_vue_plugin else '❌'} Vue插件: {'已配置' if has_vue_plugin else '未配置'}")
            print(f"{'✅' if has_port_config else '⚠️'} 端口配置: {'已配置' if has_port_config else '使用默认'}")
            print(f"{'✅' if has_proxy_config else '⚠️'} 代理配置: {'已配置' if has_proxy_config else '未配置'}")
            
        except Exception as e:
            print(f"❌ vite.config.ts 读取失败: {e}")
    else:
        print("❌ vite.config.ts: 不存在")
    
    # 检查环境变量文件
    env_files = ['.env', '.env.local', '.env.development', '.env.production']
    for env_file in env_files:
        env_path = frontend_path / env_file
        if env_path.exists():
            print(f"✅ {env_file}: 存在")
        else:
            print(f"⚠️ {env_file}: 不存在")

def check_import_issues():
    """检查导入问题"""
    project_root = Path.cwd().parent
    frontend_path = project_root / "frontend"
    
    print(f"\n📥 导入问题检查")
    print("-" * 30)
    
    # 检查 main.ts
    main_ts = frontend_path / "src" / "main.ts"
    if main_ts.exists():
        try:
            content = main_ts.read_text(encoding='utf-8')
            
            # 检查关键导入
            imports_to_check = [
                ("vue", "createApp"),
                ("vue-router", "router"),
                ("element-plus", "Element"),
                ("@/config", "config"),
                ("@/App.vue", "App")
            ]
            
            print("📋 关键导入检查:")
            for lib, desc in imports_to_check:
                if lib in content:
                    print(f"✅ {desc}: 已导入")
                else:
                    print(f"❌ {desc}: 未导入")
            
            # 检查可能的语法错误
            syntax_issues = []
            
            # 检查未闭合的括号
            open_braces = content.count('{')
            close_braces = content.count('}')
            if open_braces != close_braces:
                syntax_issues.append(f"括号不匹配: {{ {open_braces} 个, }} {close_braces} 个")
            
            # 检查导入语法
            import_lines = [line.strip() for line in content.split('\n') if line.strip().startswith('import')]
            for i, line in enumerate(import_lines):
                if not line.endswith(("'", '"')) and 'from' in line:
                    syntax_issues.append(f"第{i+1}个导入语句可能有语法错误: {line}")
            
            if syntax_issues:
                print("⚠️ 发现潜在语法问题:")
                for issue in syntax_issues:
                    print(f"   - {issue}")
            else:
                print("✅ 语法检查通过")
                
        except Exception as e:
            print(f"❌ main.ts 检查失败: {e}")
    else:
        print("❌ main.ts: 不存在")

def check_console_errors():
    """分析可能的控制台错误"""
    print(f"\n🚨 常见错误分析")
    print("-" * 30)
    
    common_issues = [
        {
            "error": "Failed to resolve module specifier",
            "cause": "模块路径解析失败",
            "solution": "检查导入路径是否正确，确保文件存在"
        },
        {
            "error": "Uncaught SyntaxError",
            "cause": "JavaScript/TypeScript 语法错误",
            "solution": "检查代码语法，特别是括号、引号匹配"
        },
        {
            "error": "Cannot read property of undefined",
            "cause": "访问未定义对象的属性",
            "solution": "检查变量初始化和空值处理"
        },
        {
            "error": "Module not found",
            "cause": "模块未找到",
            "solution": "检查依赖是否安装，路径是否正确"
        },
        {
            "error": "Network Error",
            "cause": "网络请求失败",
            "solution": "检查API地址配置和后端服务状态"
        }
    ]
    
    print("📋 常见错误类型及解决方案:")
    for issue in common_issues:
        print(f"\n❌ {issue['error']}")
        print(f"   原因: {issue['cause']}")
        print(f"   解决: {issue['solution']}")

def generate_fix_suggestions():
    """生成修复建议"""
    print(f"\n💡 修复建议")
    print("=" * 50)
    
    suggestions = [
        "1. 检查开发服务器是否正常启动",
        "2. 清除浏览器缓存和本地存储",
        "3. 重新安装依赖: rm -rf node_modules && npm install",
        "4. 检查控制台错误信息",
        "5. 确认后端API服务是否运行",
        "6. 检查网络连接和代理设置",
        "7. 尝试硬刷新页面 (Ctrl+F5)",
        "8. 检查防火墙和安全软件设置"
    ]
    
    for suggestion in suggestions:
        print(f"✅ {suggestion}")
    
    print(f"\n🔧 快速修复命令:")
    print("```bash")
    print("# 进入前端目录")
    print("cd frontend")
    print("")
    print("# 重新安装依赖")
    print("rm -rf node_modules package-lock.json")
    print("npm install")
    print("")
    print("# 重新启动开发服务器")
    print("npm run dev")
    print("```")

def main():
    """主函数"""
    print("🔍 前端空白页面诊断工具")
    print("=" * 60)
    print("正在分析可能导致页面空白的原因...\n")
    
    # 执行各项检查
    files_ok = check_frontend_files()
    deps_ok = check_dependencies()
    check_config_issues()
    check_import_issues()
    check_console_errors()
    
    # 生成总结
    print(f"\n📊 诊断总结")
    print("=" * 50)
    
    if files_ok and deps_ok:
        print("✅ 基础文件和依赖检查通过")
        print("🔍 建议检查控制台错误信息获取更多详情")
    else:
        print("❌ 发现基础问题，需要先解决文件或依赖问题")
    
    # 生成修复建议
    generate_fix_suggestions()
    
    print(f"\n✅ 诊断完成！")
    print("💡 如果问题仍然存在，请检查浏览器控制台的具体错误信息")

if __name__ == "__main__":
    main()
