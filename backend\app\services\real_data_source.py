"""
真实数据源集成服务
集成AKShare、Tushare等数据源，提供统一的数据接口
"""

import asyncio
import aiohttp
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class StockQuote:
    """股票行情数据"""
    symbol: str
    name: str
    current_price: float
    change: float
    change_percent: float
    open_price: float
    high_price: float
    low_price: float
    prev_close: float
    volume: int
    amount: float
    turnover_rate: float
    pe_ratio: Optional[float]
    update_time: datetime

@dataclass
class KLineData:
    """K线数据"""
    timestamp: int
    open: float
    high: float
    low: float
    close: float
    volume: int
    amount: float

class RealDataSource:
    """真实数据源基类"""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.cache: Dict[str, Any] = {}
        self.cache_expiry: Dict[str, datetime] = {}
        self.cache_duration = timedelta(seconds=30)  # 30秒缓存
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def is_cache_valid(self, key: str) -> bool:
        """检查缓存是否有效"""
        if key not in self.cache:
            return False
        
        expiry = self.cache_expiry.get(key)
        if not expiry:
            return False
        
        return datetime.now() < expiry
    
    def update_cache(self, key: str, data: Any) -> None:
        """更新缓存"""
        self.cache[key] = data
        self.cache_expiry[key] = datetime.now() + self.cache_duration
    
    async def get_stock_quote(self, symbol: str) -> Optional[StockQuote]:
        """获取股票实时行情"""
        raise NotImplementedError
    
    async def get_kline_data(self, symbol: str, period: str = '1d', limit: int = 100) -> List[KLineData]:
        """获取K线数据"""
        raise NotImplementedError

class AKShareDataSource(RealDataSource):
    """AKShare数据源"""
    
    def __init__(self):
        super().__init__()
        self.base_url = "http://localhost:8001"  # AKShare API服务地址
    
    async def get_stock_quote(self, symbol: str) -> Optional[StockQuote]:
        """获取股票实时行情"""
        cache_key = f"quote_{symbol}"
        
        if self.is_cache_valid(cache_key):
            return self.cache[cache_key]
        
        try:
            # 调用AKShare API获取实时行情
            url = f"{self.base_url}/api/stock_zh_a_spot_em"
            params = {"symbol": symbol}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    quote = self._parse_quote_data(symbol, data)
                    self.update_cache(cache_key, quote)
                    return quote
                else:
                    logger.error(f"AKShare API错误: {response.status}")
                    return None
        
        except Exception as e:
            logger.error(f"获取AKShare行情数据失败: {e}")
            return None
    
    async def get_kline_data(self, symbol: str, period: str = '1d', limit: int = 100) -> List[KLineData]:
        """获取K线数据"""
        cache_key = f"kline_{symbol}_{period}_{limit}"
        
        if self.is_cache_valid(cache_key):
            return self.cache[cache_key]
        
        try:
            # 调用AKShare API获取K线数据
            url = f"{self.base_url}/api/stock_zh_a_hist"
            params = {
                "symbol": symbol,
                "period": self._convert_period(period),
                "start_date": (datetime.now() - timedelta(days=limit)).strftime('%Y%m%d'),
                "end_date": datetime.now().strftime('%Y%m%d')
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    kline_data = self._parse_kline_data(data)
                    self.update_cache(cache_key, kline_data)
                    return kline_data[-limit:]  # 返回最新的limit条数据
                else:
                    logger.error(f"AKShare K线API错误: {response.status}")
                    return []
        
        except Exception as e:
            logger.error(f"获取AKShare K线数据失败: {e}")
            return []
    
    def _parse_quote_data(self, symbol: str, data: Dict) -> StockQuote:
        """解析行情数据"""
        try:
            return StockQuote(
                symbol=symbol,
                name=data.get('name', f'股票{symbol}'),
                current_price=float(data.get('current', 0)),
                change=float(data.get('change', 0)),
                change_percent=float(data.get('change_percent', 0)),
                open_price=float(data.get('open', 0)),
                high_price=float(data.get('high', 0)),
                low_price=float(data.get('low', 0)),
                prev_close=float(data.get('prev_close', 0)),
                volume=int(data.get('volume', 0)),
                amount=float(data.get('amount', 0)),
                turnover_rate=float(data.get('turnover_rate', 0)),
                pe_ratio=float(data.get('pe_ratio', 0)) if data.get('pe_ratio') else None,
                update_time=datetime.now()
            )
        except (ValueError, TypeError) as e:
            logger.error(f"解析行情数据失败: {e}")
            raise
    
    def _parse_kline_data(self, data: List[Dict]) -> List[KLineData]:
        """解析K线数据"""
        kline_data = []
        
        for item in data:
            try:
                kline_data.append(KLineData(
                    timestamp=int(datetime.strptime(item['date'], '%Y-%m-%d').timestamp() * 1000),
                    open=float(item['open']),
                    high=float(item['high']),
                    low=float(item['low']),
                    close=float(item['close']),
                    volume=int(item['volume']),
                    amount=float(item.get('amount', 0))
                ))
            except (ValueError, TypeError, KeyError) as e:
                logger.error(f"解析K线数据项失败: {e}")
                continue
        
        return kline_data
    
    def _convert_period(self, period: str) -> str:
        """转换周期格式"""
        period_map = {
            '1m': '1',
            '5m': '5',
            '15m': '15',
            '30m': '30',
            '1h': '60',
            '1d': 'daily',
            '1w': 'weekly',
            '1M': 'monthly'
        }
        return period_map.get(period, 'daily')

class TushareDataSource(RealDataSource):
    """Tushare数据源"""
    
    def __init__(self, token: str):
        super().__init__()
        self.token = token
        self.base_url = "http://api.tushare.pro"
    
    async def get_stock_quote(self, symbol: str) -> Optional[StockQuote]:
        """获取股票实时行情"""
        cache_key = f"tushare_quote_{symbol}"
        
        if self.is_cache_valid(cache_key):
            return self.cache[cache_key]
        
        try:
            # 调用Tushare API
            data = {
                "api_name": "daily",
                "token": self.token,
                "params": {
                    "ts_code": self._convert_symbol(symbol),
                    "trade_date": datetime.now().strftime('%Y%m%d')
                }
            }
            
            async with self.session.post(self.base_url, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get('code') == 0:
                        quote = self._parse_tushare_quote(symbol, result['data'])
                        self.update_cache(cache_key, quote)
                        return quote
                    else:
                        logger.error(f"Tushare API错误: {result.get('msg')}")
                        return None
                else:
                    logger.error(f"Tushare HTTP错误: {response.status}")
                    return None
        
        except Exception as e:
            logger.error(f"获取Tushare行情数据失败: {e}")
            return None
    
    async def get_kline_data(self, symbol: str, period: str = '1d', limit: int = 100) -> List[KLineData]:
        """获取K线数据"""
        cache_key = f"tushare_kline_{symbol}_{period}_{limit}"
        
        if self.is_cache_valid(cache_key):
            return self.cache[cache_key]
        
        try:
            data = {
                "api_name": "daily",
                "token": self.token,
                "params": {
                    "ts_code": self._convert_symbol(symbol),
                    "start_date": (datetime.now() - timedelta(days=limit * 2)).strftime('%Y%m%d'),
                    "end_date": datetime.now().strftime('%Y%m%d')
                }
            }
            
            async with self.session.post(self.base_url, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get('code') == 0:
                        kline_data = self._parse_tushare_kline(result['data'])
                        self.update_cache(cache_key, kline_data)
                        return kline_data[-limit:]
                    else:
                        logger.error(f"Tushare K线API错误: {result.get('msg')}")
                        return []
                else:
                    logger.error(f"Tushare K线HTTP错误: {response.status}")
                    return []
        
        except Exception as e:
            logger.error(f"获取Tushare K线数据失败: {e}")
            return []
    
    def _convert_symbol(self, symbol: str) -> str:
        """转换股票代码格式"""
        if symbol.startswith('6'):
            return f"{symbol}.SH"
        elif symbol.startswith(('0', '3')):
            return f"{symbol}.SZ"
        else:
            return symbol
    
    def _parse_tushare_quote(self, symbol: str, data: List[Dict]) -> Optional[StockQuote]:
        """解析Tushare行情数据"""
        if not data:
            return None
        
        item = data[0]  # 取最新数据
        try:
            prev_close = float(item.get('pre_close', 0))
            current_price = float(item.get('close', 0))
            change = current_price - prev_close
            change_percent = (change / prev_close * 100) if prev_close > 0 else 0
            
            return StockQuote(
                symbol=symbol,
                name=f'股票{symbol}',  # Tushare需要额外API获取名称
                current_price=current_price,
                change=change,
                change_percent=change_percent,
                open_price=float(item.get('open', 0)),
                high_price=float(item.get('high', 0)),
                low_price=float(item.get('low', 0)),
                prev_close=prev_close,
                volume=int(item.get('vol', 0)) * 100,  # Tushare单位是手
                amount=float(item.get('amount', 0)) * 1000,  # Tushare单位是千元
                turnover_rate=float(item.get('turnover_rate', 0)),
                pe_ratio=float(item.get('pe', 0)) if item.get('pe') else None,
                update_time=datetime.now()
            )
        except (ValueError, TypeError) as e:
            logger.error(f"解析Tushare行情数据失败: {e}")
            return None
    
    def _parse_tushare_kline(self, data: List[Dict]) -> List[KLineData]:
        """解析Tushare K线数据"""
        kline_data = []
        
        for item in data:
            try:
                kline_data.append(KLineData(
                    timestamp=int(datetime.strptime(item['trade_date'], '%Y%m%d').timestamp() * 1000),
                    open=float(item['open']),
                    high=float(item['high']),
                    low=float(item['low']),
                    close=float(item['close']),
                    volume=int(item['vol']) * 100,  # 转换为股
                    amount=float(item['amount']) * 1000  # 转换为元
                ))
            except (ValueError, TypeError, KeyError) as e:
                logger.error(f"解析Tushare K线数据项失败: {e}")
                continue
        
        return sorted(kline_data, key=lambda x: x.timestamp)

class DataSourceManager:
    """数据源管理器"""
    
    def __init__(self):
        self.sources: List[RealDataSource] = []
        self.primary_source: Optional[RealDataSource] = None
    
    def add_source(self, source: RealDataSource, is_primary: bool = False):
        """添加数据源"""
        self.sources.append(source)
        if is_primary or not self.primary_source:
            self.primary_source = source
    
    async def get_stock_quote(self, symbol: str) -> Optional[StockQuote]:
        """获取股票行情（带降级）"""
        # 首先尝试主要数据源
        if self.primary_source:
            try:
                quote = await self.primary_source.get_stock_quote(symbol)
                if quote:
                    return quote
            except Exception as e:
                logger.warning(f"主要数据源失败: {e}")
        
        # 尝试其他数据源
        for source in self.sources:
            if source == self.primary_source:
                continue
            
            try:
                quote = await source.get_stock_quote(symbol)
                if quote:
                    logger.info(f"使用备用数据源获取到数据: {type(source).__name__}")
                    return quote
            except Exception as e:
                logger.warning(f"备用数据源失败: {e}")
        
        return None
    
    async def get_kline_data(self, symbol: str, period: str = '1d', limit: int = 100) -> List[KLineData]:
        """获取K线数据（带降级）"""
        # 首先尝试主要数据源
        if self.primary_source:
            try:
                data = await self.primary_source.get_kline_data(symbol, period, limit)
                if data:
                    return data
            except Exception as e:
                logger.warning(f"主要数据源K线数据失败: {e}")
        
        # 尝试其他数据源
        for source in self.sources:
            if source == self.primary_source:
                continue
            
            try:
                data = await source.get_kline_data(symbol, period, limit)
                if data:
                    logger.info(f"使用备用数据源获取到K线数据: {type(source).__name__}")
                    return data
            except Exception as e:
                logger.warning(f"备用数据源K线数据失败: {e}")
        
        return []

# 创建全局数据源管理器
data_source_manager = DataSourceManager()

# 初始化数据源
async def init_data_sources():
    """初始化数据源"""
    try:
        # 添加AKShare数据源
        akshare_source = AKShareDataSource()
        data_source_manager.add_source(akshare_source, is_primary=True)
        
        # 添加Tushare数据源（需要token）
        tushare_token = "your_tushare_token_here"  # 从环境变量获取
        if tushare_token and tushare_token != "your_tushare_token_here":
            tushare_source = TushareDataSource(tushare_token)
            data_source_manager.add_source(tushare_source)
        
        logger.info("数据源初始化完成")
    except Exception as e:
        logger.error(f"数据源初始化失败: {e}")

# 导出主要接口
async def get_real_stock_quote(symbol: str) -> Optional[StockQuote]:
    """获取真实股票行情"""
    return await data_source_manager.get_stock_quote(symbol)

async def get_real_kline_data(symbol: str, period: str = '1d', limit: int = 100) -> List[KLineData]:
    """获取真实K线数据"""
    return await data_source_manager.get_kline_data(symbol, period, limit)
