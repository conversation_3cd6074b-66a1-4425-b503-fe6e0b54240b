#!/usr/bin/env python3
"""
交易中心功能测试脚本
验证交易中心的各项功能是否正常工作
"""

import asyncio
import json
import time
from pathlib import Path
from typing import Dict, List, Any

def test_vue_component_syntax(file_path: Path) -> Dict[str, Any]:
    """测试Vue组件语法正确性"""
    if not file_path.exists():
        return {"status": "error", "message": "文件不存在"}
    
    try:
        content = file_path.read_text(encoding='utf-8')
        
        # 检查基本Vue结构
        has_template = '<template>' in content and '</template>' in content
        has_script = '<script setup lang="ts">' in content and '</script>' in content
        has_style = '<style' in content and '</style>' in content
        
        # 检查TypeScript语法
        import_count = len([line for line in content.split('\n') if line.strip().startswith('import')])
        const_count = len([line for line in content.split('\n') if 'const ' in line])
        function_count = len([line for line in content.split('\n') if '=>' in line])
        
        # 检查Vue 3 Composition API
        has_ref = 'ref(' in content
        has_reactive = 'reactive(' in content
        has_computed = 'computed(' in content
        has_onMounted = 'onMounted(' in content
        
        # 检查Element Plus组件
        el_components = []
        for line in content.split('\n'):
            if '<el-' in line:
                import re
                components = re.findall(r'<(el-[\w-]+)', line)
                el_components.extend(components)
        
        return {
            "status": "success",
            "structure": {
                "has_template": has_template,
                "has_script": has_script,
                "has_style": has_style
            },
            "typescript": {
                "import_count": import_count,
                "const_count": const_count,
                "function_count": function_count
            },
            "vue3_features": {
                "has_ref": has_ref,
                "has_reactive": has_reactive,
                "has_computed": has_computed,
                "has_onMounted": has_onMounted
            },
            "ui_components": {
                "element_plus_count": len(set(el_components)),
                "components": list(set(el_components))[:10]  # 只显示前10个
            }
        }
        
    except Exception as e:
        return {"status": "error", "message": str(e)}

def test_component_imports(file_path: Path) -> Dict[str, Any]:
    """测试组件导入是否正确"""
    if not file_path.exists():
        return {"status": "error", "message": "文件不存在"}
    
    try:
        content = file_path.read_text(encoding='utf-8')
        
        # 提取导入的组件
        import re
        imports = re.findall(r'import (\w+) from [\'"](.+)[\'"]', content)
        
        # 提取模板中使用的组件
        template_match = re.search(r'<template>(.*?)</template>', content, re.DOTALL)
        if template_match:
            template_content = template_match.group(1)
            used_components = re.findall(r'<([A-Z]\w+)', template_content)
        else:
            used_components = []
        
        # 检查导入和使用是否匹配
        imported_components = [imp[0] for imp in imports if imp[0][0].isupper()]
        
        missing_imports = [comp for comp in used_components if comp not in imported_components]
        unused_imports = [comp for comp in imported_components if comp not in used_components]
        
        return {
            "status": "success",
            "imports": imports,
            "imported_components": imported_components,
            "used_components": used_components,
            "missing_imports": missing_imports,
            "unused_imports": unused_imports,
            "import_match": len(missing_imports) == 0 and len(unused_imports) == 0
        }
        
    except Exception as e:
        return {"status": "error", "message": str(e)}

def test_trading_center_functionality():
    """测试交易中心功能完整性"""
    project_root = Path.cwd().parent
    frontend_path = project_root / "frontend"
    
    # 测试文件列表
    test_files = {
        "TradingCenter": frontend_path / "src" / "views" / "Trading" / "TradingCenter.vue",
        "TradingTerminal": frontend_path / "src" / "views" / "Trading" / "modules" / "TradingTerminalModule.vue",
        "AccountManagement": frontend_path / "src" / "views" / "Trading" / "modules" / "AccountManagementModule.vue",
        "DataCenter": frontend_path / "src" / "views" / "Trading" / "modules" / "DataCenterModule.vue"
    }
    
    print("🧪 交易中心功能测试报告")
    print("=" * 60)
    
    all_passed = True
    
    for name, file_path in test_files.items():
        print(f"\n📋 测试组件: {name}")
        print("-" * 40)
        
        # 语法测试
        syntax_result = test_vue_component_syntax(file_path)
        if syntax_result["status"] == "success":
            print("✅ 语法检查: 通过")
            
            # 详细信息
            structure = syntax_result["structure"]
            if all(structure.values()):
                print("✅ Vue结构: 完整 (template + script + style)")
            else:
                print(f"⚠️ Vue结构: 部分缺失 {structure}")
            
            ts_info = syntax_result["typescript"]
            print(f"📊 TypeScript: {ts_info['import_count']}个导入, {ts_info['const_count']}个常量, {ts_info['function_count']}个函数")
            
            vue3_info = syntax_result["vue3_features"]
            vue3_features = [k for k, v in vue3_info.items() if v]
            print(f"🔧 Vue3特性: {len(vue3_features)}/4 ({', '.join(vue3_features)})")
            
            ui_info = syntax_result["ui_components"]
            print(f"🎨 UI组件: {ui_info['element_plus_count']}种Element Plus组件")
            
        else:
            print(f"❌ 语法检查: 失败 - {syntax_result['message']}")
            all_passed = False
        
        # 导入测试
        import_result = test_component_imports(file_path)
        if import_result["status"] == "success":
            if import_result["import_match"]:
                print("✅ 组件导入: 匹配")
            else:
                print("⚠️ 组件导入: 不匹配")
                if import_result["missing_imports"]:
                    print(f"   缺失导入: {import_result['missing_imports']}")
                if import_result["unused_imports"]:
                    print(f"   未使用导入: {import_result['unused_imports']}")
        else:
            print(f"❌ 导入检查: 失败 - {import_result['message']}")
            all_passed = False
    
    # 总体评估
    print(f"\n🎯 总体测试结果")
    print("=" * 60)
    
    if all_passed:
        print("🎉 所有测试通过！交易中心功能完整，可以正常使用。")
        print("\n✅ 推荐操作:")
        print("   1. 启动前端开发服务器: npm run dev")
        print("   2. 访问交易中心页面进行功能验证")
        print("   3. 测试各个模块的交互功能")
        print("   4. 验证数据导出等高级功能")
    else:
        print("⚠️ 部分测试未通过，建议检查相关问题。")
        print("\n🔧 建议操作:")
        print("   1. 检查语法错误并修复")
        print("   2. 确保组件导入正确")
        print("   3. 验证Vue 3和TypeScript语法")
        print("   4. 重新运行测试确认修复")
    
    return all_passed

def test_backend_api_availability():
    """测试后端API可用性"""
    print(f"\n🔌 后端API可用性测试")
    print("-" * 40)
    
    # 检查关键API文件
    project_root = Path.cwd().parent
    backend_path = project_root / "backend"
    
    api_files = {
        "交易API": backend_path / "app" / "api" / "v1" / "trading.py",
        "模拟交易API": backend_path / "app" / "api" / "v1" / "simulated_trading.py",
        "交易终端API": backend_path / "app" / "api" / "v1" / "trading_terminal.py",
        "风险管理API": backend_path / "app" / "api" / "v1" / "risk.py",
        "WebSocket API": backend_path / "app" / "api" / "v1" / "websocket.py"
    }
    
    api_available = 0
    for name, file_path in api_files.items():
        if file_path.exists():
            print(f"✅ {name}: 可用")
            api_available += 1
        else:
            print(f"❌ {name}: 缺失")
    
    print(f"\n📊 API可用性: {api_available}/{len(api_files)} ({api_available/len(api_files)*100:.1f}%)")
    
    if api_available == len(api_files):
        print("🎉 所有关键API都可用！")
    else:
        print("⚠️ 部分API缺失，可能影响功能完整性。")
    
    return api_available == len(api_files)

def main():
    """主测试函数"""
    print("🚀 开始交易中心功能测试...")
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 前端组件测试
    frontend_passed = test_trading_center_functionality()
    
    # 后端API测试
    backend_passed = test_backend_api_availability()
    
    # 最终评估
    print(f"\n🏆 最终测试评估")
    print("=" * 60)
    
    if frontend_passed and backend_passed:
        print("🎉 恭喜！交易中心系统测试全部通过！")
        print("✅ 前端组件: 完整可用")
        print("✅ 后端API: 完整可用")
        print("🚀 系统状态: 生产就绪")
        print("\n💡 建议下一步:")
        print("   1. 启动完整系统进行端到端测试")
        print("   2. 进行用户体验测试")
        print("   3. 准备生产环境部署")
    elif frontend_passed:
        print("✅ 前端组件测试通过，后端API需要检查")
        print("🔧 建议: 确保后端服务正常运行")
    elif backend_passed:
        print("✅ 后端API测试通过，前端组件需要检查")
        print("🔧 建议: 检查前端组件语法和导入")
    else:
        print("⚠️ 前端和后端都需要进一步检查")
        print("🔧 建议: 逐步修复发现的问题")
    
    print(f"\n✅ 测试完成！")

if __name__ == "__main__":
    main()
